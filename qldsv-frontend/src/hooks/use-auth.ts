import { useEffect, useState } from 'react'
import { useAuthStore } from '@/stores/auth'

export const useAuth = () => {
  const [isHydrated, setIsHydrated] = useState(false)
  const store = useAuthStore()

  useEffect(() => {
    // Set hydrated state on client side only
    const unsubHydrate = useAuthStore.persist.onHydrate(() => {
      setIsHydrated(false)
    })

    const unsubFinishHydration = useAuthStore.persist.onFinishHydration(() => {
      setIsHydrated(true)
    })

    // Fallback: set hydrated after a short delay if persist doesn't trigger
    const timer = setTimeout(() => {
      setIsHydrated(true)
    }, 100)

    return () => {
      unsubHydrate()
      unsubFinishHydration()
      clearTimeout(timer)
    }
  }, [])

  return {
    ...store,
    isHydrated: isHydrated && store._hasHydrated
  }
}
