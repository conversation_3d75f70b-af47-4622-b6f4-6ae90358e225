'use client'

import { useState, useMemo } from 'react'
import { Plus, Search, Filter, Download, Upload, Users } from 'lucide-react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Label } from '@/components/ui/label'
import { DataTable } from '@/components/ui/data-table'
import { useAuth } from '@/hooks/use-auth'
import { students, classes, departments } from '@/data/mock'
import type { Student } from '@/types'

export default function StudentsPage() {
  const { user, canAccessDepartment } = useAuth()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedClass, setSelectedClass] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [selectedDepartment, setSelectedDepartment] = useState('')

  // Filter students based on user permissions and filters
  const filteredStudents = useMemo(() => {
    let filtered = students.filter(student => {
      // Role-based filtering
      if (user?.role === 'KHOA' && !canAccessDepartment(student.maKhoa)) {
        return false
      }
      
      // Search filter
      if (searchTerm && !student.hoTen.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !student.maSV.toLowerCase().includes(searchTerm.toLowerCase()) &&
          !student.email.toLowerCase().includes(searchTerm.toLowerCase())) {
        return false
      }
      
      // Class filter
      if (selectedClass && student.maLop !== selectedClass) {
        return false
      }
      
      // Status filter
      if (selectedStatus && student.trangThai !== selectedStatus) {
        return false
      }
      
      // Department filter
      if (selectedDepartment && student.maKhoa !== selectedDepartment) {
        return false
      }
      
      return true
    })
    
    return filtered
  }, [students, searchTerm, selectedClass, selectedStatus, selectedDepartment, user, canAccessDepartment])

  const columns = [
    {
      key: 'maSV' as keyof Student,
      title: 'Mã SV',
      sortable: true,
      width: '120px',
      render: (value: string) => (
        <div className="font-medium text-neutral-charcoal">{value}</div>
      )
    },
    {
      key: 'hoTen' as keyof Student,
      title: 'Họ và tên',
      sortable: true,
      width: '200px',
      render: (value: string, item: Student) => (
        <div>
          <div className="font-medium text-neutral-charcoal">{value}</div>
          <div className="text-sm text-neutral-dark-gray">{item.email}</div>
        </div>
      )
    },
    {
      key: 'maLop' as keyof Student,
      title: 'Lớp',
      sortable: true,
      width: '150px',
      render: (value: string) => {
        const studentClass = classes.find(c => c.maLop === value)
        return (
          <div>
            <div className="font-medium text-neutral-charcoal">{value}</div>
            <div className="text-sm text-neutral-dark-gray">{studentClass?.tenLop}</div>
          </div>
        )
      }
    },
    {
      key: 'maKhoa' as keyof Student,
      title: 'Khoa',
      sortable: true,
      width: '150px',
      render: (value: string) => {
        const department = departments.find(d => d.maKhoa === value)
        return (
          <Badge variant="outline" className="text-xs">
            {department?.tenKhoa}
          </Badge>
        )
      }
    },
    {
      key: 'gioiTinh' as keyof Student,
      title: 'Giới tính',
      sortable: true,
      width: '100px',
      render: (value: string) => (
        <span className="text-sm text-neutral-dark-gray">{value}</span>
      )
    },
    {
      key: 'trangThai' as keyof Student,
      title: 'Trạng thái',
      sortable: true,
      width: '120px',
      render: (value: string) => {
        const statusConfig = {
          active: { label: 'Đang học', variant: 'success' as const },
          inactive: { label: 'Tạm nghỉ', variant: 'warning' as const },
          graduated: { label: 'Đã tốt nghiệp', variant: 'info' as const }
        }
        const config = statusConfig[value as keyof typeof statusConfig]
        return (
          <Badge variant={config.variant} className="text-xs">
            {config.label}
          </Badge>
        )
      }
    }
  ]

  const stats = [
    {
      title: 'Tổng sinh viên',
      value: filteredStudents.length,
      description: 'Sinh viên trong hệ thống',
      color: 'text-primary bg-primary-light-blue'
    },
    {
      title: 'Đang học',
      value: filteredStudents.filter(s => s.trangThai === 'active').length,
      description: 'Sinh viên đang học',
      color: 'text-semantic-success bg-green-100'
    },
    {
      title: 'Tạm nghỉ',
      value: filteredStudents.filter(s => s.trangThai === 'inactive').length,
      description: 'Sinh viên tạm nghỉ',
      color: 'text-semantic-warning bg-yellow-100'
    },
    {
      title: 'Đã tốt nghiệp',
      value: filteredStudents.filter(s => s.trangThai === 'graduated').length,
      description: 'Sinh viên đã tốt nghiệp',
      color: 'text-semantic-info bg-blue-100'
    }
  ]

  return (
    <DashboardLayout>
      <div className="space-y-lg">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="heading-1">Quản lý sinh viên</h1>
            <p className="body-text mt-2">
              Quản lý thông tin và trạng thái học tập của sinh viên
            </p>
          </div>
          <div className="flex items-center space-x-3 mt-4 lg:mt-0">
            <Button variant="outline" size="sm">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button className="btn-primary">
              <Plus className="h-4 w-4 mr-2" />
              Thêm sinh viên
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-lg">
          {stats.map((stat, index) => (
            <Card key={index} className="stat-card">
              <CardContent className="p-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="flex items-center space-x-2 mb-2">
                      <div className={`stat-icon ${stat.color}`}>
                        <Users className="h-5 w-5" />
                      </div>
                      <h3 className="caption-text">{stat.title}</h3>
                    </div>
                    <div className="stat-number mb-1">{stat.value}</div>
                    <p className="small-text">{stat.description}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Filters */}
        <Card className="stat-card">
          <CardHeader>
            <CardTitle className="heading-3">Bộ lọc</CardTitle>
            <CardDescription className="caption-text">
              Lọc danh sách sinh viên theo các tiêu chí
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {/* Search */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-neutral-charcoal">
                  Tìm kiếm
                </Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-dark-gray" />
                  <Input
                    placeholder="Tên, mã SV, email..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Department Filter */}
              {user?.role === 'PGV' && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-neutral-charcoal">
                    Khoa
                  </Label>
                  <select
                    value={selectedDepartment}
                    onChange={(e) => setSelectedDepartment(e.target.value)}
                    className="w-full search-input"
                  >
                    <option value="">Tất cả khoa</option>
                    {departments.map((dept) => (
                      <option key={dept.maKhoa} value={dept.maKhoa}>
                        {dept.tenKhoa}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* Class Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-neutral-charcoal">
                  Lớp
                </Label>
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="w-full search-input"
                >
                  <option value="">Tất cả lớp</option>
                  {classes
                    .filter(c => user?.role !== 'KHOA' || c.maKhoa === user.maKhoa)
                    .map((cls) => (
                    <option key={cls.maLop} value={cls.maLop}>
                      {cls.tenLop}
                    </option>
                  ))}
                </select>
              </div>

              {/* Status Filter */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-neutral-charcoal">
                  Trạng thái
                </Label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full search-input"
                >
                  <option value="">Tất cả trạng thái</option>
                  <option value="active">Đang học</option>
                  <option value="inactive">Tạm nghỉ</option>
                  <option value="graduated">Đã tốt nghiệp</option>
                </select>
              </div>

              {/* Clear Filters */}
              <div className="space-y-2">
                <Label className="text-sm font-medium text-neutral-charcoal">
                  &nbsp;
                </Label>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedClass('')
                    setSelectedStatus('')
                    setSelectedDepartment('')
                  }}
                  className="w-full"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Xóa bộ lọc
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Data Table */}
        <Card className="stat-card">
          <CardHeader>
            <CardTitle className="heading-3">
              Danh sách sinh viên ({filteredStudents.length})
            </CardTitle>
            <CardDescription className="caption-text">
              Thông tin chi tiết về sinh viên trong hệ thống
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              title=""
              columns={columns}
              data={filteredStudents}
              searchable={false}
              addable={true}
              editable={true}
              deletable={true}
              onAdd={() => console.log('Add student')}
              onEdit={(student) => console.log('Edit student:', student)}
              onDelete={(student) => console.log('Delete student:', student)}
              onExport={() => console.log('Export students')}
            />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
