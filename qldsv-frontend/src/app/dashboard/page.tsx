'use client'

import { useEffect, useState } from 'react'
import { 
  Users, 
  BookOpen, 
  GraduationCap, 
  Calculator, 
  TrendingUp, 
  Calendar,
  FileText,
  DollarSign,
  Clock,
  AlertCircle,
  CheckCircle,
  BarChart3
} from 'lucide-react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/hooks/use-auth'
import { departments, students, creditClasses, subjects, teachers } from '@/data/mock'
import type { UserRole } from '@/types'

interface StatCardProps {
  title: string
  value: string | number
  description: string
  icon: React.ComponentType<{ className?: string }>
  trend?: {
    value: number
    isPositive: boolean
  }
  color?: 'primary' | 'success' | 'warning' | 'info'
}

const StatCard = ({ title, value, description, icon: Icon, trend, color = 'primary' }: StatCardProps) => {
  const colorClasses = {
    primary: 'text-primary bg-primary-light-blue',
    success: 'text-semantic-success bg-green-100',
    warning: 'text-semantic-warning bg-yellow-100',
    info: 'text-semantic-info bg-blue-100'
  }

  return (
    <Card className="stat-card">
      <CardContent className="p-lg">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <div className={`stat-icon ${colorClasses[color]}`}>
                <Icon className="h-5 w-5" />
              </div>
              <h3 className="caption-text">{title}</h3>
            </div>
            <div className="stat-number mb-1">{value}</div>
            <p className="small-text">{description}</p>
          </div>
          {trend && (
            <div className={`flex items-center text-sm ${
              trend.isPositive ? 'text-semantic-success' : 'text-semantic-error'
            }`}>
              <TrendingUp className={`h-4 w-4 mr-1 ${!trend.isPositive ? 'rotate-180' : ''}`} />
              {Math.abs(trend.value)}%
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface QuickActionProps {
  title: string
  description: string
  icon: React.ComponentType<{ className?: string }>
  href: string
  variant?: 'primary' | 'secondary'
}

const QuickAction = ({ title, description, icon: Icon, href, variant = 'secondary' }: QuickActionProps) => {
  const cardClass = variant === 'primary' ? 'course-card' : 'course-card-secondary'
  const textClass = variant === 'primary' ? 'course-card-text' : 'text-neutral-charcoal'

  return (
    <Card className={cardClass}>
      <CardContent className="p-lg">
        <div className="flex items-center space-x-3 mb-3">
          <div className={variant === 'primary' ? 'course-icon' : 'stat-icon'}>
            <Icon className="h-6 w-6" />
          </div>
          <div>
            <h3 className={`text-lg font-semibold ${textClass}`}>{title}</h3>
            <p className={`text-sm ${variant === 'primary' ? 'text-neutral-white/80' : 'text-neutral-dark-gray'}`}>
              {description}
            </p>
          </div>
        </div>
        <Button 
          className={variant === 'primary' ? 'bg-white/20 text-neutral-white hover:bg-white/30' : 'btn-primary'}
          size="sm"
        >
          Truy cập
        </Button>
      </CardContent>
    </Card>
  )
}

export default function DashboardPage() {
  const { user, selectedDepartment, isHydrated } = useAuth()
  const [currentTime, setCurrentTime] = useState<Date | null>(null)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    setCurrentTime(new Date())
    const timer = setInterval(() => setCurrentTime(new Date()), 1000)
    return () => clearInterval(timer)
  }, [])

  // Show loading state during hydration
  if (!isHydrated || !isMounted) {
    return <div className="flex items-center justify-center min-h-screen">
      <div className="text-neutral-dark-gray">Đang tải...</div>
    </div>
  }

  if (!user) return null

  // Get role-specific data
  const getDashboardData = () => {
    const baseStats = {
      totalStudents: students.length,
      totalTeachers: teachers.length,
      totalSubjects: subjects.length,
      totalCreditClasses: creditClasses.length,
      activeCreditClasses: creditClasses.filter(cc => !cc.huyLop).length
    }

    switch (user.role) {
      case 'PGV':
        return {
          stats: [
            {
              title: 'Tổng sinh viên',
              value: baseStats.totalStudents,
              description: 'Sinh viên đang học',
              icon: GraduationCap,
              trend: { value: 12, isPositive: true },
              color: 'primary' as const
            },
            {
              title: 'Lớp tín chỉ',
              value: baseStats.activeCreditClasses,
              description: 'Lớp đang hoạt động',
              icon: BookOpen,
              trend: { value: 8, isPositive: true },
              color: 'success' as const
            },
            {
              title: 'Giảng viên',
              value: baseStats.totalTeachers,
              description: 'Giảng viên đang giảng dạy',
              icon: Users,
              color: 'info' as const
            },
            {
              title: 'Môn học',
              value: baseStats.totalSubjects,
              description: 'Môn học trong chương trình',
              icon: Calculator,
              color: 'warning' as const
            }
          ],
          quickActions: [
            {
              title: 'Quản lý lớp tín chỉ',
              description: 'Mở lớp, phân công giảng viên',
              icon: Calendar,
              href: '/data/credit-classes',
              variant: 'primary' as const
            },
            {
              title: 'Nhập điểm',
              description: 'Nhập và quản lý điểm số',
              icon: FileText,
              href: '/academic/grades'
            },
            {
              title: 'Báo cáo thống kê',
              description: 'Xem báo cáo tổng hợp',
              icon: BarChart3,
              href: '/reports'
            },
            {
              title: 'Quản lý sinh viên',
              description: 'Thông tin sinh viên',
              icon: GraduationCap,
              href: '/data/students'
            }
          ]
        }

      case 'KHOA':
        const departmentStudents = students.filter(s => s.maKhoa === user.maKhoa)
        const departmentTeachers = teachers.filter(t => t.maKhoa === user.maKhoa)
        return {
          stats: [
            {
              title: 'Sinh viên khoa',
              value: departmentStudents.length,
              description: `Khoa ${departments.find(d => d.maKhoa === user.maKhoa)?.tenKhoa}`,
              icon: GraduationCap,
              color: 'primary' as const
            },
            {
              title: 'Giảng viên khoa',
              value: departmentTeachers.length,
              description: 'Giảng viên thuộc khoa',
              icon: Users,
              color: 'success' as const
            },
            {
              title: 'Lớp tín chỉ',
              value: creditClasses.filter(cc => 
                teachers.find(t => t.maGV === cc.maGV)?.maKhoa === user.maKhoa && !cc.huyLop
              ).length,
              description: 'Lớp đang diễn ra',
              icon: BookOpen,
              color: 'info' as const
            }
          ],
          quickActions: [
            {
              title: 'Sinh viên khoa',
              description: 'Quản lý sinh viên trong khoa',
              icon: GraduationCap,
              href: '/data/students',
              variant: 'primary' as const
            },
            {
              title: 'Lớp tín chỉ khoa',
              description: 'Lớp tín chỉ của khoa',
              icon: BookOpen,
              href: '/data/credit-classes'
            },
            {
              title: 'Báo cáo khoa',
              description: 'Báo cáo thống kê khoa',
              icon: FileText,
              href: '/reports'
            }
          ]
        }

      case 'SV':
        const studentCreditClasses = creditClasses.filter(cc => 
          // Mock: assume student is registered for some classes
          cc.maMH === 'IT001' || cc.maMH === 'IT002'
        )
        return {
          stats: [
            {
              title: 'Lớp đã đăng ký',
              value: studentCreditClasses.length,
              description: 'Học kỳ hiện tại',
              icon: BookOpen,
              color: 'primary' as const
            },
            {
              title: 'Tín chỉ tích lũy',
              value: 45,
              description: 'Tín chỉ đã hoàn thành',
              icon: Calculator,
              color: 'success' as const
            },
            {
              title: 'Điểm trung bình',
              value: '3.2',
              description: 'GPA tích lũy',
              icon: TrendingUp,
              color: 'info' as const
            }
          ],
          quickActions: [
            {
              title: 'Đăng ký học phần',
              description: 'Đăng ký lớp tín chỉ mới',
              icon: Calendar,
              href: '/academic/registration',
              variant: 'primary' as const
            },
            {
              title: 'Xem bảng điểm',
              description: 'Kết quả học tập',
              icon: FileText,
              href: '/reports/transcripts'
            },
            {
              title: 'Học phí',
              description: 'Thông tin học phí',
              icon: DollarSign,
              href: '/fees/view'
            }
          ]
        }

      case 'PKT':
        return {
          stats: [
            {
              title: 'Tổng học phí',
              value: '2.4B',
              description: 'Học phí năm học',
              icon: DollarSign,
              color: 'primary' as const
            },
            {
              title: 'Đã thu',
              value: '1.8B',
              description: 'Đã thanh toán',
              icon: CheckCircle,
              color: 'success' as const
            },
            {
              title: 'Chưa thu',
              value: '600M',
              description: 'Còn phải thu',
              icon: AlertCircle,
              color: 'warning' as const
            }
          ],
          quickActions: [
            {
              title: 'Quản lý học phí',
              description: 'Thu và quản lý học phí',
              icon: DollarSign,
              href: '/fees/management',
              variant: 'primary' as const
            },
            {
              title: 'Báo cáo tài chính',
              description: 'Thống kê thu chi',
              icon: BarChart3,
              href: '/reports/fees'
            },
            {
              title: 'Theo dõi nợ',
              description: 'Sinh viên chưa đóng phí',
              icon: AlertCircle,
              href: '/reports/debts'
            }
          ]
        }

      default:
        return { stats: [], quickActions: [] }
    }
  }

  const { stats, quickActions } = getDashboardData()

  const getRoleDisplayName = (role: UserRole) => {
    const roleNames = {
      PGV: 'Phòng Giáo vụ',
      KHOA: 'Khoa',
      SV: 'Sinh viên',
      PKT: 'Phòng Kế toán'
    }
    return roleNames[role]
  }

  return (
    <DashboardLayout>
      <div className="space-y-lg">
        {/* Welcome Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h1 className="heading-1">
              Chào mừng, {user.hoTen}
            </h1>
            <div className="flex items-center space-x-2 mt-2">
              <Badge className="bg-primary text-neutral-white">
                {getRoleDisplayName(user.role)}
              </Badge>
              {user.maKhoa && (
                <Badge variant="outline">
                  {departments.find(d => d.maKhoa === user.maKhoa)?.tenKhoa}
                </Badge>
              )}
              {selectedDepartment && user.role === 'PGV' && (
                <Badge variant="outline">
                  Đang quản lý: {departments.find(d => d.maKhoa === selectedDepartment)?.tenKhoa}
                </Badge>
              )}
            </div>
          </div>
          <div className="mt-4 lg:mt-0">
            {isMounted && currentTime && (
              <>
                <div className="flex items-center space-x-2 text-neutral-dark-gray">
                  <Clock className="h-4 w-4" />
                  <span className="caption-text">
                    {currentTime.toLocaleDateString('vi-VN', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </div>
                <div className="caption-text text-neutral-dark-gray">
                  {currentTime.toLocaleTimeString('vi-VN')}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-lg">
          {stats.map((stat, index) => (
            <StatCard key={index} {...stat} />
          ))}
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="heading-2 mb-lg">Thao tác nhanh</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-lg">
            {quickActions.map((action, index) => (
              <QuickAction key={index} {...action} />
            ))}
          </div>
        </div>

        {/* Recent Activity or Additional Widgets */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-lg">
          {/* Announcements */}
          <Card className="stat-card">
            <CardHeader>
              <CardTitle className="heading-3">Thông báo</CardTitle>
              <CardDescription className="caption-text">
                Thông tin mới nhất từ hệ thống
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-3 bg-primary-light-blue rounded-lg">
                  <AlertCircle className="h-5 w-5 text-primary mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-neutral-charcoal">
                      Thời gian đăng ký học phần
                    </h4>
                    <p className="text-xs text-neutral-dark-gray mt-1">
                      Đăng ký học phần học kỳ II năm học 2023-2024 từ ngày 15/12/2023
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-3 bg-yellow-50 rounded-lg">
                  <Clock className="h-5 w-5 text-semantic-warning mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-neutral-charcoal">
                      Hạn nộp học phí
                    </h4>
                    <p className="text-xs text-neutral-dark-gray mt-1">
                      Hạn cuối nộp học phí học kỳ II: 31/12/2023
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Schedule or Calendar Widget */}
          <Card className="stat-card">
            <CardHeader>
              <CardTitle className="heading-3">Lịch hôm nay</CardTitle>
              <CardDescription className="caption-text">
                Các hoạt động trong ngày
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {user.role === 'SV' ? (
                  // Student schedule
                  <>
                    <div className="flex items-center justify-between p-3 border border-neutral-medium-gray rounded-lg">
                      <div>
                        <h4 className="text-sm font-medium text-neutral-charcoal">
                          Nhập môn lập trình
                        </h4>
                        <p className="text-xs text-neutral-dark-gray">
                          Tiết 1-3 • Phòng A101
                        </p>
                      </div>
                      <Badge className="badge-success">8:00</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border border-neutral-medium-gray rounded-lg">
                      <div>
                        <h4 className="text-sm font-medium text-neutral-charcoal">
                          Cấu trúc dữ liệu
                        </h4>
                        <p className="text-xs text-neutral-dark-gray">
                          Tiết 4-6 • Phòng B201
                        </p>
                      </div>
                      <Badge className="badge-info">13:00</Badge>
                    </div>
                  </>
                ) : (
                  // Admin/Teacher schedule
                  <>
                    <div className="flex items-center justify-between p-3 border border-neutral-medium-gray rounded-lg">
                      <div>
                        <h4 className="text-sm font-medium text-neutral-charcoal">
                          Họp khoa
                        </h4>
                        <p className="text-xs text-neutral-dark-gray">
                          Phòng họp A
                        </p>
                      </div>
                      <Badge className="badge-warning">9:00</Badge>
                    </div>
                    <div className="flex items-center justify-between p-3 border border-neutral-medium-gray rounded-lg">
                      <div>
                        <h4 className="text-sm font-medium text-neutral-charcoal">
                          Kiểm tra đăng ký
                        </h4>
                        <p className="text-xs text-neutral-dark-gray">
                          Xem xét đăng ký học phần
                        </p>
                      </div>
                      <Badge className="badge-info">14:00</Badge>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
