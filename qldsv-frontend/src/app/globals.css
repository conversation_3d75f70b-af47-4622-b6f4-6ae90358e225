@import "tailwindcss";

:root {
  --background: #f8fafc;
  --foreground: #1e293b;
  --primary: #4a90e2;
  --primary-dark: #357abd;
  --primary-light: #e8f4fd;
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Component styles */
.btn-primary {
  background-color: var(--primary);
  color: white;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.btn-secondary {
  background-color: white;
  color: var(--primary);
  border: 1px solid var(--primary);
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: var(--primary-light);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
