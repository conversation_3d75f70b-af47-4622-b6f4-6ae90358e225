'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Eye, EyeOff, GraduationCap } from 'lucide-react'
import { useAuth } from '@/hooks/use-auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { departments } from '@/data/mock'
import type { UserRole } from '@/types'

const loginSchema = z.object({
  username: z.string().min(1, 'Tên đăng nhập là bắt buộc'),
  password: z.string().min(1, '<PERSON><PERSON><PERSON> khẩu là bắt buộc'),
  role: z.enum(['PGV', 'KHOA', 'SV', 'PKT'], {
    required_error: 'Vui lòng chọn vai trò'
  }),
  maKhoa: z.string().optional()
})

type LoginFormData = z.infer<typeof loginSchema>

const roles: { value: UserRole; label: string; description: string; color: string }[] = [
  {
    value: 'PGV',
    label: 'Phòng Giáo vụ',
    description: 'Quản lý toàn bộ hệ thống',
    color: 'bg-primary text-neutral-white'
  },
  {
    value: 'KHOA',
    label: 'Khoa',
    description: 'Quản lý trong phạm vi khoa',
    color: 'bg-accent-teal text-neutral-white'
  },
  {
    value: 'SV',
    label: 'Sinh viên',
    description: 'Đăng ký học phần, xem điểm',
    color: 'bg-accent-purple text-neutral-white'
  },
  {
    value: 'PKT',
    label: 'Phòng Kế toán',
    description: 'Quản lý học phí',
    color: 'bg-accent-orange text-neutral-white'
  }
]

export default function LoginPage() {
  const router = useRouter()
  const { login, isAuthenticated, isHydrated } = useAuth()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      role: 'SV'
    }
  })

  const selectedRole = watch('role')

  useEffect(() => {
    if (isHydrated && isAuthenticated) {
      router.push('/dashboard')
    }
  }, [isAuthenticated, isHydrated, router])

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true)
    try {
      const success = await login(data.username, data.password, data.role, data.maKhoa)
      if (success) {
        router.push('/dashboard')
      } else {
        alert('Đăng nhập thất bại. Vui lòng kiểm tra lại thông tin.')
      }
    } catch (error) {
      console.error('Login error:', error)
      alert('Có lỗi xảy ra khi đăng nhập.')
    } finally {
      setIsLoading(false)
    }
  }

  if (!isHydrated) {
    return (
      <div className="min-h-screen bg-neutral-light-gray flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-neutral-dark-gray">Đang tải...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-neutral-light-gray flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-primary rounded-lg p-3">
              <GraduationCap className="h-8 w-8 text-neutral-white" />
            </div>
          </div>
          <h1 className="heading-1 mb-2">Hệ thống quản lý sinh viên</h1>
          <p className="body-text">Đăng nhập để tiếp tục</p>
        </div>

        {/* Login Form */}
        <Card className="stat-card">
          <CardHeader className="text-center pb-4">
            <CardTitle className="heading-3">Đăng nhập</CardTitle>
            <CardDescription className="caption-text">
              Chọn vai trò và nhập thông tin đăng nhập
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Role Selection */}
              <div className="space-y-3">
                <Label className="text-sm font-medium text-neutral-charcoal">
                  Vai trò
                </Label>
                <div className="grid grid-cols-2 gap-3">
                  {roles.map((role) => (
                    <button
                      key={role.value}
                      type="button"
                      onClick={() => setValue('role', role.value)}
                      className={`p-3 rounded-lg border-2 text-left transition-all duration-hover ${
                        selectedRole === role.value
                          ? 'border-primary bg-primary-light-blue'
                          : 'border-neutral-medium-gray bg-neutral-white hover:border-primary-dark-blue'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <Badge className={`text-xs ${role.color}`}>
                          {role.value}
                        </Badge>
                        {selectedRole === role.value && (
                          <div className="w-2 h-2 bg-primary rounded-full" />
                        )}
                      </div>
                      <div className="text-sm font-medium text-neutral-charcoal">
                        {role.label}
                      </div>
                      <div className="text-xs text-neutral-dark-gray mt-1">
                        {role.description}
                      </div>
                    </button>
                  ))}
                </div>
                {errors.role && (
                  <p className="text-xs text-semantic-error">{errors.role.message}</p>
                )}
              </div>

              {/* Department Selection for KHOA role */}
              {selectedRole === 'KHOA' && (
                <div className="space-y-2">
                  <Label htmlFor="maKhoa" className="text-sm font-medium text-neutral-charcoal">
                    Khoa
                  </Label>
                  <select
                    {...register('maKhoa')}
                    className="w-full search-input"
                  >
                    <option value="">Chọn khoa</option>
                    {departments.map((dept) => (
                      <option key={dept.maKhoa} value={dept.maKhoa}>
                        {dept.tenKhoa}
                      </option>
                    ))}
                  </select>
                  {errors.maKhoa && (
                    <p className="text-xs text-semantic-error">{errors.maKhoa.message}</p>
                  )}
                </div>
              )}

              {/* Department Selection for PGV role */}
              {selectedRole === 'PGV' && (
                <div className="space-y-2">
                  <Label htmlFor="maKhoa" className="text-sm font-medium text-neutral-charcoal">
                    Khoa quản lý (tùy chọn)
                  </Label>
                  <select
                    {...register('maKhoa')}
                    className="w-full search-input"
                  >
                    <option value="">Tất cả các khoa</option>
                    {departments.map((dept) => (
                      <option key={dept.maKhoa} value={dept.maKhoa}>
                        {dept.tenKhoa}
                      </option>
                    ))}
                  </select>
                  <p className="text-xs text-neutral-dark-gray">
                    PGV có thể chọn khoa cụ thể hoặc quản lý tất cả
                  </p>
                </div>
              )}

              {/* Username */}
              <div className="space-y-2">
                <Label htmlFor="username" className="text-sm font-medium text-neutral-charcoal">
                  Tên đăng nhập
                </Label>
                <Input
                  id="username"
                  {...register('username')}
                  placeholder="Nhập tên đăng nhập"
                  className="search-input"
                />
                {errors.username && (
                  <p className="text-xs text-semantic-error">{errors.username.message}</p>
                )}
              </div>

              {/* Password */}
              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium text-neutral-charcoal">
                  Mật khẩu
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    {...register('password')}
                    placeholder="Nhập mật khẩu"
                    className="search-input pr-10"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-dark-gray hover:text-neutral-charcoal"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="text-xs text-semantic-error">{errors.password.message}</p>
                )}
              </div>

              {/* Demo Accounts Info */}
              <div className="bg-primary-light-blue p-4 rounded-lg">
                <h4 className="text-sm font-medium text-neutral-charcoal mb-2">
                  Tài khoản demo:
                </h4>
                <div className="space-y-1 text-xs text-neutral-dark-gray">
                  <p>• PGV: pgv001 / password</p>
                  <p>• KHOA: khoa001 / password</p>
                  <p>• SV: sv001 / password</p>
                  <p>• PKT: pkt001 / password</p>
                </div>
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                className="w-full btn-primary"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-neutral-white mr-2"></div>
                    Đang đăng nhập...
                  </div>
                ) : (
                  'Đăng nhập'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="small-text">
            Hệ thống quản lý sinh viên tín chỉ © 2024
          </p>
        </div>
      </div>
    </div>
  )
}
