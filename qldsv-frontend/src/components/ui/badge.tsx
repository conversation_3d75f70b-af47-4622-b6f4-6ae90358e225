import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-xl px-2 py-1 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "bg-primary text-neutral-white hover:bg-primary-dark-blue",
        secondary: "bg-neutral-medium-gray text-neutral-charcoal hover:bg-neutral-dark-gray",
        destructive: "bg-semantic-error text-neutral-white hover:bg-red-600",
        outline: "border border-neutral-medium-gray text-neutral-charcoal hover:bg-neutral-light-gray",
        success: "bg-semantic-success text-neutral-white hover:bg-green-600",
        warning: "bg-semantic-warning text-neutral-white hover:bg-yellow-600",
        info: "bg-semantic-info text-neutral-white hover:bg-blue-600",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
