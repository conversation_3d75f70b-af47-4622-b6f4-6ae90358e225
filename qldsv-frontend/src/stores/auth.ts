import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User, AuthState, UserRole } from '@/types'

interface AuthStore extends AuthState {
  login: (username: string, password: string, role: UserRole, maKhoa?: string) => Promise<boolean>
  logout: () => void
  setSelectedDepartment: (maKhoa: string) => void
  checkPermission: (requiredRole: UserRole | UserRole[]) => boolean
  canAccessDepartment: (maKhoa: string) => boolean
  _hasHydrated: boolean
  setHasHydrated: (state: boolean) => void
}

// Mock users for development
const mockUsers: User[] = [
  {
    id: '1',
    username: 'pgv001',
    role: 'PGV',
    hoTen: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
    email: '<EMAIL>'
  },
  {
    id: '2',
    username: 'khoa001',
    role: 'KH<PERSON>',
    maKhoa: 'CNTT',
    hoTen: 'Trần T<PERSON>nh',
    email: '<EMAIL>'
  },
  {
    id: '3',
    username: 'sv001',
    role: 'SV',
    maKhoa: 'CNTT',
    hoTen: 'Lê Văn Cường',
    email: '<EMAIL>'
  },
  {
    id: '4',
    username: 'pkt001',
    role: 'PKT',
    hoTen: 'Phạm Thị Dung',
    email: '<EMAIL>'
  }
]

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      selectedDepartment: undefined,
      _hasHydrated: false,

      setHasHydrated: (state) => {
        set({
          _hasHydrated: state
        })
      },

      login: async (username: string, password: string, role: UserRole, maKhoa?: string) => {
        // Mock authentication - in real app, this would call an API
        const user = mockUsers.find(u =>
          u.username === username &&
          u.role === role &&
          (role !== 'KHOA' || u.maKhoa === maKhoa)
        )

        if (user && password === 'password') {
          set({
            user,
            isAuthenticated: true,
            selectedDepartment: user.role === 'PGV' ? maKhoa : user.maKhoa
          })
          return true
        }

        return false
      },

      logout: () => {
        set({
          user: null,
          isAuthenticated: false,
          selectedDepartment: undefined
        })
      },

      setSelectedDepartment: (maKhoa: string) => {
        const { user } = get()
        if (user?.role === 'PGV') {
          set({ selectedDepartment: maKhoa })
        }
      },

      checkPermission: (requiredRole: UserRole | UserRole[]) => {
        const { user } = get()
        if (!user) return false

        const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole]
        return roles.includes(user.role)
      },

      canAccessDepartment: (maKhoa: string) => {
        const { user, selectedDepartment } = get()
        if (!user) return false

        switch (user.role) {
          case 'PGV':
            return true // PGV can access all departments
          case 'KHOA':
            return user.maKhoa === maKhoa
          case 'SV':
            return user.maKhoa === maKhoa
          case 'PKT':
            return true // PKT can access all departments for fee management
          default:
            return false
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        selectedDepartment: state.selectedDepartment
      }),
      onRehydrateStorage: () => (state) => {
        state?.setHasHydrated(true)
      },
    }
  )
)

// Export a hook that includes hydration status
export const useAuth = () => {
  const store = useAuthStore()
  return {
    ...store,
    isHydrated: store._hasHydrated
  }
}
